<?php
/**
 * Vanatex Filter Manager - WordPress Admin Tool
 * Tool voor het beheren van shop filters per product categorie
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

class Vanatex_Filter_Manager {

    private $option_name = 'vanatex_category_filters';

    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'admin_init'));
        add_action('wp_ajax_vanatex_save_category_filters', array($this, 'save_category_filters'));
    }

    /**
     * Add admin menu item
     */
    public function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            'Filter Manager',
            'Filter Manager',
            'manage_woocommerce',
            'vanatex-filter-manager',
            array($this, 'admin_page')
        );
    }

    /**
     * Initialize admin settings
     */
    public function admin_init() {
        register_setting('vanatex_filter_manager', $this->option_name);
    }

    /**
     * Get all parent product categories
     */
    private function get_parent_categories() {
        $categories = get_terms(array(
            'taxonomy' => 'product_cat',
            'hide_empty' => false,
            'parent' => 0
        ));

        return is_wp_error($categories) ? array() : $categories;
    }

    /**
     * Get special pages (like main shop page)
     */
    private function get_special_pages() {
        return array(
            (object) array(
                'term_id' => 'shop',
                'name' => __('Hoofdshop Pagina', 'textdomain'),
                'slug' => 'shop'
            )
        );
    }

    /**
     * Get all product attributes
     */
    private function get_product_attributes() {
        $attributes = wc_get_attribute_taxonomies();
        return $attributes ? $attributes : array();
    }

    /**
     * Get saved filter configuration
     */
    private function get_saved_filters() {
        return get_option($this->option_name, array());
    }

    /**
     * Admin page content
     */
    public function admin_page() {
        $parent_categories = $this->get_parent_categories();
        $special_pages = $this->get_special_pages();
        $attributes = $this->get_product_attributes();
        $saved_filters = $this->get_saved_filters();

        ?>
        <div class="wrap">
            <h1>Filter Manager</h1>
            <p>Selecteer welke filters bij welke product categorieën weergegeven moeten worden.</p>

            <?php if (isset($_GET['updated']) && $_GET['updated'] === 'true'): ?>
                <div class="notice notice-success is-dismissible">
                    <p>Filter configuratie opgeslagen!</p>
                </div>
            <?php endif; ?>

            <form method="post" action="options.php" id="filter-manager-form">
                <?php settings_fields('vanatex_filter_manager'); ?>

                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th scope="col" style="width: 25%;">Product Categorie</th>
                            <th scope="col">Beschikbare Filters</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // Combine special pages and categories
                        $all_items = array_merge($special_pages, $parent_categories);
                        ?>
                        <?php if (empty($all_items)): ?>
                            <tr>
                                <td colspan="2">Geen categorieën of pagina's gevonden.</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($all_items as $item): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo esc_html($item->name); ?></strong>
                                        <br>
                                        <small><?php echo esc_html($item->slug); ?></small>
                                    </td>
                                    <td>
                                        <?php if (empty($attributes)): ?>
                                            <em>Geen product attributes gevonden.</em>
                                        <?php else: ?>
                                            <?php
                                            $item_filters = isset($saved_filters[$item->term_id]) ? $saved_filters[$item->term_id] : array();
                                            ?>
                                            <div class="filter-checkboxes">
                                                <?php foreach ($attributes as $attribute): ?>
                                                    <label style="display: block; margin-bottom: 5px;">
                                                        <input type="checkbox"
                                                               name="<?php echo esc_attr($this->option_name); ?>[<?php echo esc_attr($item->term_id); ?>][]"
                                                               value="<?php echo esc_attr($attribute->attribute_name); ?>"
                                                               <?php checked(in_array($attribute->attribute_name, $item_filters)); ?>>
                                                        <?php echo esc_html($attribute->attribute_label); ?>
                                                        <small>(<?php echo esc_html($attribute->attribute_name); ?>)</small>
                                                    </label>
                                                <?php endforeach; ?>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>

                <?php submit_button('Configuratie Opslaan'); ?>
            </form>

            <div class="postbox" style="margin-top: 20px;">
                <div class="postbox-header">
                    <h2>Instructies</h2>
                </div>
                <div class="inside">
                    <p><strong>Hoe werkt dit?</strong></p>
                    <ul>
                        <li>Selecteer per parent categorie welke filters getoond moeten worden</li>
                        <li>Alleen geselecteerde filters worden weergegeven in de shop voor die categorie</li>
                        <li>Als geen filters geselecteerd zijn, worden alle filters getoond (standaard gedrag)</li>
                        <li>Configuratie geldt voor de parent categorie en alle subcategorieën</li>
                    </ul>
                </div>
            </div>

            <div class="postbox" style="margin-top: 20px;">
                <div class="postbox-header">
                    <h2>Debug Informatie</h2>
                </div>
                <div class="inside">
                    <p><strong>Huidige configuratie:</strong></p>
                    <pre><?php print_r($saved_filters); ?></pre>
                </div>
            </div>
        </div>

        <style>
        .filter-checkboxes {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: #f9f9f9;
        }
        </style>
        <?php
    }

    /**
     * Get filters for a specific category or page (including parent lookup)
     */
    public static function get_filters_for_category($category_id) {
        $saved_filters = get_option('vanatex_category_filters', array());

        // Debug logging for admins
        if (current_user_can('manage_options')) {
            error_log('Vanatex Filter Manager - Category ID: ' . $category_id . ', Saved filters: ' . print_r($saved_filters, true));
            error_log('Vanatex Filter Manager - Looking for key: ' . $category_id . ', Key exists: ' . (isset($saved_filters[$category_id]) ? 'YES' : 'NO'));
            if (isset($saved_filters[$category_id])) {
                error_log('Vanatex Filter Manager - Found filters: ' . print_r($saved_filters[$category_id], true));
            }
        }

        // Handle special case for main shop page
        if ($category_id === 0 || $category_id === '0') {
            // Check for shop page configuration
            if (isset($saved_filters['shop'])) {
                if (current_user_can('manage_options')) {
                    error_log('Vanatex Filter Manager - Found shop configuration: ' . print_r($saved_filters['shop'], true));
                    error_log('Vanatex Filter Manager - Shop config is empty: ' . (empty($saved_filters['shop']) ? 'YES' : 'NO'));
                }
                return $saved_filters['shop']; // Return even if empty - empty means "no filters configured"
            } else {
                if (current_user_can('manage_options')) {
                    error_log('Vanatex Filter Manager - No shop configuration found');
                }
                return array(); // No configuration found, show all filters
            }
        }

        // Convert category_id to string for array key lookup (WordPress sometimes stores as string)
        $category_id_str = (string) $category_id;

        // First check if current category has filters configured
        if (isset($saved_filters[$category_id]) && !empty($saved_filters[$category_id])) {
            return $saved_filters[$category_id];
        }

        // Also check with string key
        if (isset($saved_filters[$category_id_str]) && !empty($saved_filters[$category_id_str])) {
            return $saved_filters[$category_id_str];
        }

        // If not, check parent category (only for actual categories, not for shop page)
        if ($category_id !== 0 && $category_id !== '0') {
            $category = get_term($category_id, 'product_cat');
            if ($category && $category->parent > 0) {
                $parent_id = $category->parent;
                $parent_id_str = (string) $parent_id;

                if (isset($saved_filters[$parent_id]) && !empty($saved_filters[$parent_id])) {
                    return $saved_filters[$parent_id];
                }

                if (isset($saved_filters[$parent_id_str]) && !empty($saved_filters[$parent_id_str])) {
                    return $saved_filters[$parent_id_str];
                }
            }
        }

        // Return empty array if no configuration found (will show all filters)
        return array();
    }

    /**
     * Check if there's a filter configuration for a specific category or page
     */
    public static function has_filter_configuration($category_id) {
        $saved_filters = get_option('vanatex_category_filters', array());

        // Debug the input
        if (current_user_can('manage_options')) {
            error_log('Vanatex Filter Manager - has_filter_configuration called with: ' . print_r($category_id, true) . ' (type: ' . gettype($category_id) . ')');
            error_log('Vanatex Filter Manager - saved_filters keys: ' . print_r(array_keys($saved_filters), true));
        }

        // Handle special case for main shop page
        if ($category_id === 0 || $category_id === '0') {
            $has_shop_config = isset($saved_filters['shop']);
            if (current_user_can('manage_options')) {
                error_log('Vanatex Filter Manager - Shop page detected, has_shop_config: ' . ($has_shop_config ? 'YES' : 'NO'));
                if ($has_shop_config) {
                    error_log('Vanatex Filter Manager - Shop config value: ' . print_r($saved_filters['shop'], true));
                }
            }
            return $has_shop_config;
        }

        // Convert category_id to string for array key lookup
        $category_id_str = (string) $category_id;

        // Check if current category has filters configured
        if (isset($saved_filters[$category_id]) && !empty($saved_filters[$category_id])) {
            return true;
        }

        // Also check with string key
        if (isset($saved_filters[$category_id_str]) && !empty($saved_filters[$category_id_str])) {
            return true;
        }

        // Check parent category (only for actual categories, not for shop page)
        if ($category_id !== 0 && $category_id !== '0') {
            $category = get_term($category_id, 'product_cat');
            if ($category && $category->parent > 0) {
                $parent_id = $category->parent;
                $parent_id_str = (string) $parent_id;

                if (isset($saved_filters[$parent_id]) && !empty($saved_filters[$parent_id])) {
                    return true;
                }

                if (isset($saved_filters[$parent_id_str]) && !empty($saved_filters[$parent_id_str])) {
                    return true;
                }
            }
        }

        return false;
    }
}

// Initialize the filter manager
new Vanatex_Filter_Manager();
